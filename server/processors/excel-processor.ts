import { BasePayrollProcessor, PayrollData } from './base-processor.ts';

/**
 * Excel Payroll Processor
 * Handles processing and generation of Excel (.xlsx, .xls) payroll files
 * Uses dynamic import to handle ExcelJS ES module compatibility
 */
export class ExcelProcessor extends BasePayrollProcessor {
  getSupportedExtensions(): string[] {
    return ['.xlsx', '.xls'];
  }

  getMimeType(): string {
    return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  }

  async process(buffer: Buffer, filename: string): Promise<PayrollData> {
    try {
      // Dynamic import to handle ExcelJS ES module compatibility
      const ExcelJS = await import('exceljs');
      const workbook = new ExcelJS.default.Workbook();
      await workbook.xlsx.load(buffer);

      const worksheet = workbook.getWorksheet(1);
      if (!worksheet) {
        throw new Error('El archivo Excel no contiene hojas de cálculo');
      }

      const jsonData: any[][] = [];
      worksheet.eachRow((row: any, rowNumber: any) => {
        const rowData: any[] = [];
        row.eachCell((cell: any, colNumber: any) => {
          rowData[colNumber - 1] = cell.value;
        });
        jsonData.push(rowData);
      });

      if (jsonData.length < 2) {
        throw new Error('El archivo Excel debe tener al menos una fila de encabezados y una fila de datos');
      }

      // Convert to object format
      const headers = jsonData[0].map((h: any) =>
        String(h || '').toLowerCase().replace(/\s+/g, '_')
      );
      
      const rows = jsonData.slice(1).map(row => {
        const obj: any = {};
        headers.forEach((header, index) => {
          obj[header] = row[index] || '';
        });
        return obj;
      }).filter(row => Object.values(row).some(val => val !== ''));

      const normalizedData = this.normalizeData({ entries: rows });
      
      // Validate the processed data
      this.validateEmployee(normalizedData.employee);
      this.validateEntries(normalizedData.entries);

      return normalizedData;
    } catch (error) {
      throw this.createError('processing', error);
    }
  }

  async generate(data: PayrollData): Promise<Buffer> {
    try {
      // Dynamic import to handle ExcelJS ES module compatibility
      const ExcelJS = await import('exceljs');
      const workbook = new ExcelJS.default.Workbook();

      // Employee sheet
      const employeeSheet = workbook.addWorksheet('Empleado');
      const employeeData = [
        ['Campo', 'Valor'],
        ['Brigada', data.employee.brigade],
        ['Expediente', data.employee.fileNumber],
        ['Colaborador', data.employee.name],
        ['CI', data.employee.ci],
        ['Firma', data.employee.signature],
        ['Centro de Costo', data.employee.costCenter],
        ['Unidad Orgánica', data.employee.organizationalUnit]
      ];
      employeeSheet.addRows(employeeData);

      // Concepts sheet
      const conceptsSheet = workbook.addWorksheet('Conceptos');
      const conceptsData = [
        ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio'],
        ...data.entries.map(entry => [
          entry.concept.section,
          entry.concept.name,
          entry.entry.amountKz || 0,
          entry.entry.amountUsd || 0,
          entry.entry.exchangeRate
        ])
      ];
      conceptsSheet.addRows(conceptsData);

      return Buffer.from(await workbook.xlsx.writeBuffer());
    } catch (error) {
      throw this.createError('generation', error);
    }
  }
}
