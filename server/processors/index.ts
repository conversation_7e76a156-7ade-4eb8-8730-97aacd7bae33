// Export runtime classes
export { BasePayrollProcessor } from './base-processor.ts';
export { CsvProcessor } from './csv-processor.ts';
export { ExcelProcessor } from './excel-processor.ts';
export { JsonProcessor } from './json-processor.ts';
export { XmlProcessor } from './xml-processor.ts';
export { MarkdownProcessor } from './markdown-processor.ts';

// Export types and interfaces (compile-time only)
export type { PayrollProcessor, PayrollData, RawPayrollEntry, RawEmployeeData } from './base-processor.ts';

// Processor factory for easy instantiation
import type { PayrollProcessor } from './base-processor.ts';
import { CsvProcessor } from './csv-processor.ts';
import { ExcelProcessor } from './excel-processor.ts';
import { JsonProcessor } from './json-processor.ts';
import { XmlProcessor } from './xml-processor.ts';
import { MarkdownProcessor } from './markdown-processor.ts';

/**
 * Factory class for creating format-specific processors
 */
export class ProcessorFactory {
  private static processors: Map<string, () => PayrollProcessor> = new Map([
    ['.csv', () => new CsvProcessor()],
    ['.xlsx', () => new ExcelProcessor()],
    ['.xls', () => new ExcelProcessor()],
    ['.json', () => new JsonProcessor()],
    ['.xml', () => new XmlProcessor()],
    ['.md', () => new MarkdownProcessor()],
  ]);

  /**
   * Get a processor instance for the given file extension
   * @param extension - File extension (e.g., '.csv', '.xlsx')
   * @returns PayrollProcessor instance
   * @throws Error if extension is not supported
   */
  static getProcessor(extension: string): PayrollProcessor {
    const processorFactory = this.processors.get(extension.toLowerCase());
    if (!processorFactory) {
      const supportedExtensions = Array.from(this.processors.keys()).join(', ');
      throw new Error(`Unsupported file format: ${extension}. Supported formats: ${supportedExtensions}`);
    }
    return processorFactory();
  }

  /**
   * Get all supported file extensions
   * @returns Array of supported extensions
   */
  static getSupportedExtensions(): string[] {
    return Array.from(this.processors.keys());
  }

  /**
   * Check if a file extension is supported
   * @param extension - File extension to check
   * @returns True if supported, false otherwise
   */
  static isSupported(extension: string): boolean {
    return this.processors.has(extension.toLowerCase());
  }

  /**
   * Get MIME type for a given extension
   * @param extension - File extension
   * @returns MIME type string
   * @throws Error if extension is not supported
   */
  static getMimeType(extension: string): string {
    const processor = this.getProcessor(extension);
    return processor.getMimeType();
  }
}
