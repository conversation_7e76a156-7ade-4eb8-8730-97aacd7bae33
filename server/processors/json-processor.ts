import { BasePayrollProcessor, PayrollData } from './base-processor.ts';

/**
 * JSON Payroll Processor
 * Handles processing and generation of JSON payroll files
 */
export class JsonProcessor extends BasePayrollProcessor {
  getSupportedExtensions(): string[] {
    return ['.json'];
  }

  getMimeType(): string {
    return 'application/json';
  }

  async process(buffer: Buffer, filename: string): Promise<PayrollData> {
    try {
      const jsonContent = buffer.toString('utf-8');
      const data = JSON.parse(jsonContent);
      
      let normalizedData: PayrollData;

      if (Array.isArray(data)) {
        // Handle array of entries
        normalizedData = this.normalizeData({ entries: data });
      } else if (typeof data === 'object') {
        // Handle different JSON structures
        if (data.entries || data.employee || data.year) {
          // Already structured payroll data
          normalizedData = this.normalizeData(data);
        } else {
          // Single entry object
          normalizedData = this.normalizeData({ entries: [data] });
        }
      } else {
        throw new Error('Estructura JSON no válida');
      }
      
      // Validate the processed data
      this.validateEmployee(normalizedData.employee);
      this.validateEntries(normalizedData.entries);

      return normalizedData;
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw this.createError('processing', new Error('Archivo JSON malformado: ' + error.message));
      }
      throw this.createError('processing', error);
    }
  }

  async generate(data: PayrollData): Promise<Buffer> {
    try {
      const exportData = {
        year: data.report.year,
        month: data.report.month,
        exchangeRate: data.report.exchangeRate,
        employee: data.employee,
        entries: data.entries.map(entry => ({
          section: entry.concept.section,
          concept: entry.concept.name,
          amountKz: entry.entry.amountKz,
          amountUsd: entry.entry.amountUsd,
          exchangeRate: entry.entry.exchangeRate
        }))
      };

      return Buffer.from(JSON.stringify(exportData, null, 2), 'utf-8');
    } catch (error) {
      throw this.createError('generation', error);
    }
  }
}
