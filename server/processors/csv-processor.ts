import { BasePayrollProcessor, PayrollData } from './base-processor.ts';

/**
 * CSV Payroll Processor
 * Handles processing and generation of CSV payroll files
 */
export class CsvProcessor extends BasePayrollProcessor {
  getSupportedExtensions(): string[] {
    return ['.csv'];
  }

  getMimeType(): string {
    return 'text/csv';
  }

  async process(buffer: Buffer, filename: string): Promise<PayrollData> {
    try {
      const csvContent = buffer.toString('utf-8');
      const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line);
      
      if (lines.length < 2) {
        throw new Error('El archivo CSV debe tener al menos una fila de encabezados y una fila de datos');
      }

      // Simple CSV parser
      const parseCSVLine = (line: string): string[] => {
        const result: string[] = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
          const char = line[i];
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
          } else {
            current += char;
          }
        }
        result.push(current.trim());
        return result;
      };

      // Parse CSV into sections (employee data and entries)
      const employeeRows: any[] = [];
      const conceptRows: any[] = [];
      let currentHeaders: string[] = [];
      let isEmployeeSection = false;
      let isConceptSection = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (!line) continue; // Skip empty lines

        const values = parseCSVLine(line);

        // Check if this line looks like employee headers
        if (values.some(v => v.toLowerCase().includes('brigada') ||
                           v.toLowerCase().includes('expediente') ||
                           v.toLowerCase().includes('colaborador'))) {
          currentHeaders = values.map(h => h.toLowerCase().replace(/\s+/g, '_'));
          isEmployeeSection = true;
          isConceptSection = false;
          continue;
        }

        // Check if this line looks like concept headers
        if (values.some(v => v.toLowerCase().includes('sección') ||
                           v.toLowerCase().includes('concepto'))) {
          currentHeaders = values.map(h => h.toLowerCase().replace(/\s+/g, '_'));
          isEmployeeSection = false;
          isConceptSection = true;
          continue;
        }

        // If we have headers and this is a data row
        if (currentHeaders.length > 0 && values.length >= currentHeaders.length) {
          const row: any = {};
          currentHeaders.forEach((header, index) => {
            row[header] = values[index] || '';
          });

          if (isEmployeeSection) {
            employeeRows.push(row);
          } else if (isConceptSection) {
            conceptRows.push(row);
          }
        }
      }

      // Prepare data for normalization
      const rawData: any = {
        entries: conceptRows
      };

      // Add employee data if found
      if (employeeRows.length > 0) {
        const emp = employeeRows[0]; // Take first employee row
        rawData.brigade = emp.brigada || emp.brigade;
        rawData.fileNumber = emp.expediente || emp.file_number;
        rawData.name = emp.colaborador || emp.name;
        rawData.ci = emp.ci;
        rawData.signature = emp.firma || emp.signature;
        rawData.costCenter = emp.centro_de_costo || emp.cost_center;
        rawData.organizationalUnit = emp.unidad_orgánica || emp.organizational_unit;
      }

      const normalizedData = this.normalizeData(rawData);
      
      // Validate the processed data
      this.validateEmployee(normalizedData.employee);
      this.validateEntries(normalizedData.entries);

      return normalizedData;
    } catch (error) {
      throw this.createError('processing', error);
    }
  }

  async generate(data: PayrollData): Promise<Buffer> {
    try {
      const rows = [
        ['Brigada', 'Expediente', 'Colaborador', 'CI', 'Firma', 'Centro de Costo', 'Unidad Orgánica'],
        [
          data.employee.brigade,
          data.employee.fileNumber,
          data.employee.name,
          data.employee.ci,
          data.employee.signature,
          data.employee.costCenter,
          data.employee.organizationalUnit
        ],
        [],
        ['Sección', 'Concepto', 'Saldo KZ', 'Saldo USD', 'Tasa de Cambio'],
        ...data.entries.map(entry => [
          entry.concept.section,
          entry.concept.name,
          (entry.entry.amountKz || 0).toString(),
          (entry.entry.amountUsd || 0).toString(),
          entry.entry.exchangeRate.toString()
        ])
      ];

      const csvContent = rows.map(row => 
        row.map(cell => `"${cell}"`).join(',')
      ).join('\n');

      return Buffer.from(csvContent, 'utf-8');
    } catch (error) {
      throw this.createError('generation', error);
    }
  }
}
