import { BasePayrollProcessor, PayrollData } from './base-processor.ts';

/**
 * Markdown Payroll Processor
 * Handles processing and generation of Markdown (.md) payroll files
 */
export class MarkdownProcessor extends BasePayrollProcessor {
  getSupportedExtensions(): string[] {
    return ['.md'];
  }

  getMimeType(): string {
    return 'text/markdown';
  }

  async process(buffer: Buffer, filename: string): Promise<PayrollData> {
    try {
      const markdown = buffer.toString('utf-8');
      const lines = markdown.split('\n');
      
      // Extract metadata
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      let exchangeRate = 931.00;
      
      const yearMatch = markdown.match(/Año:\s*(\d{4})/i);
      const monthMatch = markdown.match(/Mes:\s*(\d{1,2})/i);
      
      if (yearMatch) year = parseInt(yearMatch[1]);
      if (monthMatch) month = parseInt(monthMatch[1]);

      // Find tables
      const tables: any[][] = [];
      let currentTable: any[] = [];
      let headers: string[] = [];
      let isInTable = false;

      for (const line of lines) {
        const trimmedLine = line.trim();
        
        if (trimmedLine.includes('|') && trimmedLine.length > 1) {
          const cells = trimmedLine.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== '');
          
          if (!isInTable) {
            headers = cells.map(h => h.toLowerCase().replace(/\s+/g, '_'));
            isInTable = true;
          } else if (trimmedLine.includes('---')) {
            continue;
          } else if (cells.length >= headers.length) {
            const row: any = {};
            cells.forEach((cell, index) => {
              if (headers[index]) {
                row[headers[index]] = cell;
              }
            });
            currentTable.push(row);
          }
        } else if (isInTable && (trimmedLine === '' || !trimmedLine.includes('|'))) {
          if (currentTable.length > 0) {
            tables.push([...currentTable]);
            currentTable = [];
          }
          isInTable = false;
          headers = [];
        }
      }
      
      if (currentTable.length > 0) {
        tables.push(currentTable);
      }

      const processedData = {
        year,
        month,
        exchangeRate,
        tables
      };

      const normalizedData = this.normalizeMarkdownData(processedData);
      
      // Validate the processed data
      this.validateEmployee(normalizedData.employee);
      this.validateEntries(normalizedData.entries);

      return normalizedData;
    } catch (error) {
      throw this.createError('processing', error);
    }
  }

  async generate(data: PayrollData): Promise<Buffer> {
    try {
      const markdown = `## Informe de Resumen Mensual de Nómina

Año: ${data.report.year}  Mes: ${data.report.month}

| Brigada | Expediente | Colaborador | CI | Firma | Centro de Costo | Unidad Orgánica |
| --- | --- | --- | --- | --- | --- | --- |
| ${data.employee.brigade} | ${data.employee.fileNumber} | ${data.employee.name} | ${data.employee.ci} | ${data.employee.signature} | ${data.employee.costCenter} | ${data.employee.organizationalUnit} |

| Sección | Concepto | Saldo KZ | Saldo USD | Tasa de Cambio |
| --- | --- | --- | --- | --- |
${data.entries.map(entry => 
  `| ${entry.concept.section} | ${entry.concept.name} | $ ${(entry.entry.amountKz || 0).toFixed(2)} | $ ${(entry.entry.amountUsd || 0).toFixed(2)} | ${(entry.entry.exchangeRate || 931).toFixed(2)} |`
).join('\n')}`;

      return Buffer.from(markdown, 'utf-8');
    } catch (error) {
      throw this.createError('generation', error);
    }
  }

  /**
   * Normalize markdown-specific data structure
   * @param data - Processed markdown data with tables
   * @returns Normalized PayrollData
   */
  private normalizeMarkdownData(data: any): PayrollData {
    // Flatten all tables into a single array of rows
    const allRows = data.tables.flat();
    
    // Group rows by type (employee, concepts, etc.)
    const employeeData: any[] = [];
    const conceptData: any[] = [];
    
    for (const row of allRows) {
      // Detect if this row contains employee information
      if (row.brigada || row.brigade || row.expediente || row.file_number || row.colaborador || row.name) {
        employeeData.push(row);
      }
      // Detect if this row contains concept information
      else if (row.seccion || row.section || row.concepto || row.concept) {
        conceptData.push(row);
      }
    }
    
    const normalizedData = {
      employeeData,
      conceptData,
      year: data.year,
      month: data.month,
      exchangeRate: data.exchangeRate
    };

    return this.normalizeData(normalizedData);
  }
}
