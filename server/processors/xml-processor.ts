import * as xml2js from 'xml2js';
import { BasePayrollProcessor, PayrollData } from './base-processor.ts';

/**
 * XML Payroll Processor
 * Handles processing and generation of XML payroll files
 */
export class XmlProcessor extends BasePayrollProcessor {
  getSupportedExtensions(): string[] {
    return ['.xml'];
  }

  getMimeType(): string {
    return 'application/xml';
  }

  async process(buffer: Buffer, filename: string): Promise<PayrollData> {
    try {
      const xmlContent = buffer.toString('utf-8');
      const parser = new xml2js.Parser({ explicitArray: false, mergeAttrs: true });
      
      const result = await new Promise((resolve, reject) => {
        parser.parseString(xmlContent, (err: any, result: any) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
      
      const extractedData = this.extractDataFromXML(result);
      const normalizedData = this.normalizeData(extractedData);
      
      // Validate the processed data
      this.validateEmployee(normalizedData.employee);
      this.validateEntries(normalizedData.entries);

      return normalizedData;
    } catch (error) {
      throw this.createError('processing', error);
    }
  }

  async generate(data: PayrollData): Promise<Buffer> {
    try {
      const xmlData = {
        payroll: {
          metadata: {
            year: data.report.year,
            month: data.report.month,
            exchangeRate: data.report.exchangeRate
          },
          employee: data.employee,
          entries: data.entries.map(entry => ({
            section: entry.concept.section,
            concept: entry.concept.name,
            amountKz: entry.entry.amountKz,
            amountUsd: entry.entry.amountUsd,
            exchangeRate: entry.entry.exchangeRate
          }))
        }
      };

      const builder = new xml2js.Builder({ rootName: 'payroll' });
      const xml = builder.buildObject(xmlData);
      return Buffer.from(xml, 'utf-8');
    } catch (error) {
      throw this.createError('generation', error);
    }
  }

  /**
   * Extract structured data from parsed XML result
   * @param xmlResult - Parsed XML object
   * @returns Extracted data object
   */
  private extractDataFromXML(xmlResult: any): any {
    // Handle different XML structures
    const data: any = {
      employeeData: [],
      conceptData: [],
      year: new Date().getFullYear(),
      month: new Date().getMonth() + 1,
      exchangeRate: 931.00
    };

    if (xmlResult.payroll) {
      const payroll = xmlResult.payroll;
      
      if (payroll.metadata) {
        data.year = parseInt(payroll.metadata.year) || data.year;
        data.month = parseInt(payroll.metadata.month) || data.month;
        data.exchangeRate = parseFloat(payroll.metadata.exchangeRate) || data.exchangeRate;
      }
      
      if (payroll.employee) {
        data.employeeData.push(payroll.employee);
      }
      
      if (payroll.entries && Array.isArray(payroll.entries)) {
        data.conceptData = payroll.entries;
      }
    }

    return data;
  }
}
