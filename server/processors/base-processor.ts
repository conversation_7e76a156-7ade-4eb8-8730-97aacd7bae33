import type { 
  InsertEmployee, 
  InsertPayrollConcept, 
  InsertPayrollEntry, 
  InsertPayrollReport
} from "@shared/schema";

export interface PayrollData {
  report: InsertPayrollReport;
  employee: InsertEmployee;
  entries: Array<{
    concept: InsertPayrollConcept;
    entry: Omit<InsertPayrollEntry, 'reportId' | 'employeeId' | 'conceptId'>;
  }>;
}

export interface RawPayrollEntry {
  section: string;
  concept: string;
  amountKz: number;
  amountUsd: number;
  exchangeRate: number;
}

export interface RawEmployeeData {
  brigade: string;
  fileNumber: string;
  name: string;
  ci: string;
  signature?: string;
  costCenter: string;
  organizationalUnit: string;
}

/**
 * Common interface that all payroll processors must implement
 */
export interface PayrollProcessor {
  /**
   * Process a file buffer and return structured payroll data
   * @param buffer - The file buffer to process
   * @param filename - The original filename (for context/validation)
   * @returns Promise<PayrollData> - Structured payroll data
   * @throws Error with descriptive message if processing fails
   */
  process(buffer: Buffer, filename: string): Promise<PayrollData>;

  /**
   * Generate a file buffer from payroll data
   * @param data - The payroll data to export
   * @returns Promise<Buffer> - The generated file buffer
   * @throws Error with descriptive message if generation fails
   */
  generate(data: PayrollData): Promise<Buffer>;

  /**
   * Get the supported file extensions for this processor
   * @returns string[] - Array of supported extensions (e.g., ['.csv'])
   */
  getSupportedExtensions(): string[];

  /**
   * Get the MIME type for files generated by this processor
   * @returns string - MIME type (e.g., 'text/csv')
   */
  getMimeType(): string;
}

/**
 * Base class with common functionality for all processors
 */
export abstract class BasePayrollProcessor implements PayrollProcessor {
  abstract process(buffer: Buffer, filename: string): Promise<PayrollData>;
  abstract generate(data: PayrollData): Promise<Buffer>;
  abstract getSupportedExtensions(): string[];
  abstract getMimeType(): string;

  /**
   * Normalize and validate raw payroll data
   * @param rawData - Raw data from file processing
   * @returns PayrollData - Structured and validated payroll data
   */
  protected normalizeData(rawData: any): PayrollData {
    // Extract metadata
    const year = rawData.year || new Date().getFullYear();
    const month = rawData.month || new Date().getMonth() + 1;
    const exchangeRate = rawData.exchangeRate || 931.00;

    // Extract employee data
    const employee: InsertEmployee = {
      brigade: rawData.employee?.brigade || rawData.brigade || '',
      fileNumber: rawData.employee?.fileNumber || rawData.fileNumber || '',
      name: rawData.employee?.name || rawData.name || '',
      ci: rawData.employee?.ci || rawData.ci || '',
      signature: rawData.employee?.signature || rawData.signature || '',
      costCenter: rawData.employee?.costCenter || rawData.costCenter || '',
      organizationalUnit: rawData.employee?.organizationalUnit || rawData.organizationalUnit || ''
    };

    // Extract entries
    const entries = (rawData.entries || []).map((entry: any) => ({
      concept: {
        section: entry.section || '',
        name: entry.concept || entry.name || '',
        description: entry.description || ''
      } as InsertPayrollConcept,
      entry: {
        amountKz: parseFloat(entry.amountKz || entry.amount_kz || 0),
        amountUsd: parseFloat(entry.amountUsd || entry.amount_usd || 0),
        exchangeRate: parseFloat(entry.exchangeRate || entry.exchange_rate || exchangeRate)
      }
    }));

    return {
      report: {
        year,
        month,
        exchangeRate,
        totalKz: entries.reduce((sum: number, e: any) => sum + (e.entry.amountKz || 0), 0),
        totalUsd: entries.reduce((sum: number, e: any) => sum + (e.entry.amountUsd || 0), 0),
        status: 'draft' as const
      },
      employee,
      entries
    };
  }

  /**
   * Validate that required employee fields are present
   * @param employee - Employee data to validate
   * @throws Error if validation fails
   */
  protected validateEmployee(employee: any): void {
    const requiredFields = ['name', 'ci', 'fileNumber'];
    const missingFields = requiredFields.filter(field => !employee[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`Missing required employee fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Validate that entries data is present and valid
   * @param entries - Entries data to validate
   * @throws Error if validation fails
   */
  protected validateEntries(entries: any[]): void {
    if (!entries || entries.length === 0) {
      throw new Error('No payroll entries found in file');
    }

    entries.forEach((entry, index) => {
      if (!entry.concept && !entry.name) {
        throw new Error(`Entry ${index + 1} is missing concept/name`);
      }
    });
  }

  /**
   * Create a standardized error message for this processor
   * @param operation - The operation that failed ('processing' or 'generation')
   * @param originalError - The original error that occurred
   * @returns Error with formatted message
   */
  protected createError(operation: 'processing' | 'generation', originalError: any): Error {
    const processorName = this.constructor.name;
    const message = originalError instanceof Error ? originalError.message : 'Unknown error';
    return new Error(`${processorName} ${operation} failed: ${message}`);
  }
}
