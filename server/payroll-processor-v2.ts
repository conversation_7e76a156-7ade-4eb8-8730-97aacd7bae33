import { ProcessorFactory } from './processors/index.ts';
import type { PayrollData } from './processors/index.ts';

/**
 * Refactored PayrollProcessorV2 - Now acts as a factory/coordinator
 * Delegates to format-specific processors for better error isolation and maintainability
 */
export class PayrollProcessorV2 {
  /**
   * Process a payroll file using the appropriate format-specific processor
   * @param buffer - File buffer to process
   * @param filename - Original filename for extension detection
   * @returns Promise<PayrollData> - Structured payroll data
   * @throws <PERSON>rror with clear indication of which processor failed
   */
  static async processFile(buffer: Buffer, filename: string): Promise<PayrollData> {
    const extension = '.' + filename.split('.').pop()?.toLowerCase();
    
    try {
      // Get the appropriate processor for this file format
      const processor = ProcessorFactory.getProcessor(extension);
      
      // Delegate processing to the format-specific processor
      return await processor.process(buffer, filename);
    } catch (error) {
      // Enhanced error reporting with processor identification
      const processorName = this.getProcessorName(extension);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`${processorName} failed to process file "${filename}":`, error);
      throw new Error(`${processorName} processing failed: ${errorMessage}`);
    }
  }

  /**
   * Generate a file in the specified format from payroll data
   * @param payrollData - Structured payroll data to export
   * @param format - Target format ('csv', 'xlsx', 'json', 'xml', 'md')
   * @returns Promise<Buffer> - Generated file buffer
   * @throws Error with clear indication of which processor failed
   */
  static async generateExport(payrollData: PayrollData, format: 'csv' | 'xlsx' | 'json' | 'xml' | 'md'): Promise<Buffer> {
    const extension = `.${format}`;
    
    try {
      // Get the appropriate processor for this format
      const processor = ProcessorFactory.getProcessor(extension);
      
      // Delegate generation to the format-specific processor
      return await processor.generate(payrollData);
    } catch (error) {
      // Enhanced error reporting with processor identification
      const processorName = this.getProcessorName(extension);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`${processorName} failed to generate ${format} export:`, error);
      throw new Error(`${processorName} generation failed: ${errorMessage}`);
    }
  }

  /**
   * Get list of supported file extensions
   * @returns Array of supported extensions
   */
  static getSupportedExtensions(): string[] {
    return ProcessorFactory.getSupportedExtensions();
  }

  /**
   * Check if a file format is supported
   * @param extension - File extension to check
   * @returns True if supported, false otherwise
   */
  static isFormatSupported(extension: string): boolean {
    return ProcessorFactory.isSupported(extension);
  }

  /**
   * Get MIME type for a given file extension
   * @param extension - File extension
   * @returns MIME type string
   */
  static getMimeType(extension: string): string {
    return ProcessorFactory.getMimeType(extension);
  }

  /**
   * Get a human-readable processor name for error reporting
   * @param extension - File extension
   * @returns Processor name
   */
  private static getProcessorName(extension: string): string {
    const processorNames: Record<string, string> = {
      '.csv': 'CSV Processor',
      '.xlsx': 'Excel Processor',
      '.xls': 'Excel Processor',
      '.json': 'JSON Processor',
      '.xml': 'XML Processor',
      '.md': 'Markdown Processor'
    };
    
    return processorNames[extension] || `${extension.toUpperCase()} Processor`;
  }
}
